import 'package:flutter/material.dart';
import 'package:octasync_client/imports.dart';

/// 示例数据模型
class ExampleTreeData implements TreeNodeData {
  final String _id;
  final String _name;
  final String? _parentId;
  final List<String> _parentIdList;

  ExampleTreeData({
    required String id,
    required String name,
    String? parentId,
    List<String>? parentIdList,
  }) : _id = id,
       _name = name,
       _parentId = parentId,
       _parentIdList = parentIdList ?? [];

  @override
  String get id => _id;

  @override
  String get name => _name;

  @override
  String? get parentId => _parentId;

  @override
  List<String> get parentIdList => _parentIdList;
}

/// 树组件示例页面
class TreeExamplePage extends StatefulWidget {
  const TreeExamplePage({super.key});

  @override
  State<TreeExamplePage> createState() => _TreeExamplePageState();
}

class _TreeExamplePageState extends State<TreeExamplePage> {
  List<AppTreeNode<ExampleTreeData>> _treeNodes = [];
  String _searchQuery = '';
  bool _showCheckbox = false;
  bool _isLoading = false;
  String? _errorMessage;
  final List<ExampleTreeData> _selectedNodes = [];

  @override
  void initState() {
    super.initState();
    _loadSampleData();
  }

  /// 加载示例数据
  void _loadSampleData() {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    // 模拟异步加载
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _treeNodes = _buildSampleTree();
          _isLoading = false;
        });
      }
    });
  }

  /// 构建示例树数据
  List<AppTreeNode<ExampleTreeData>> _buildSampleTree() {
    // 创建示例数据
    final sampleData = [
      ExampleTreeData(id: '1', name: '根节点 1', parentIdList: []),
      ExampleTreeData(id: '1-1', name: '子节点 1-1', parentId: '1', parentIdList: ['1']),
      ExampleTreeData(id: '1-2', name: '子节点 1-2', parentId: '1', parentIdList: ['1']),
      ExampleTreeData(id: '1-1-1', name: '孙节点 1-1-1', parentId: '1-1', parentIdList: ['1', '1-1']),
      ExampleTreeData(id: '1-1-2', name: '孙节点 1-1-2', parentId: '1-1', parentIdList: ['1', '1-1']),
      ExampleTreeData(id: '1-2-1', name: '孙节点 1-2-1', parentId: '1-2', parentIdList: ['1', '1-2']),

      ExampleTreeData(id: '2', name: '根节点 2', parentIdList: []),
      ExampleTreeData(id: '2-1', name: '子节点 2-1', parentId: '2', parentIdList: ['2']),
      ExampleTreeData(id: '2-2', name: '子节点 2-2', parentId: '2', parentIdList: ['2']),
      ExampleTreeData(id: '2-1-1', name: '孙节点 2-1-1', parentId: '2-1', parentIdList: ['2', '2-1']),

      ExampleTreeData(id: '3', name: '根节点 3', parentIdList: []),
      ExampleTreeData(id: '3-1', name: '子节点 3-1', parentId: '3', parentIdList: ['3']),
    ];

    return _buildTreeFromData(sampleData);
  }

  /// 从数据列表构建树结构
  List<AppTreeNode<ExampleTreeData>> _buildTreeFromData(List<ExampleTreeData> data) {
    final nodeMap = <String, AppTreeNode<ExampleTreeData>>{};
    final rootNodes = <AppTreeNode<ExampleTreeData>>[];

    // 第一遍：创建所有节点
    for (final item in data) {
      final node = AppTreeNode<ExampleTreeData>(data: item);
      nodeMap[item.id] = node;
    }

    // 第二遍：构建父子关系
    for (final item in data) {
      final currentNode = nodeMap[item.id]!;

      if (item.parentId?.isNotEmpty == true) {
        // 子节点：添加到父节点
        final parentNode = nodeMap[item.parentId!];
        parentNode?.children.add(currentNode);
      } else {
        // 根节点
        rootNodes.add(currentNode);
      }
    }

    return rootNodes;
  }

  /// 模拟加载错误
  void _simulateError() {
    setState(() {
      _isLoading = false;
      _errorMessage = '加载数据时发生错误，请重试';
      _treeNodes = [];
    });
  }

  /// 重试加载
  void _retryLoad() {
    _loadSampleData();
  }

  /// 处理节点点击
  void _handleNodeTap(ExampleTreeData data) {
    ToastManager.info('点击了节点: ${data.name}');
  }

  /// 处理节点选择
  void _handleNodeSelected(ExampleTreeData data, bool isSelected) {
    setState(() {
      // 如果是取消选中操作，需要先找到对应的树节点并取消选中
      if (!isSelected) {
        _uncheckNodeById(data.id);
      }

      // 清空之前的选择列表，重新收集所有选中的节点
      _selectedNodes.clear();
      _collectCheckedNodes(_treeNodes, _selectedNodes);
    });
  }

  /// 递归收集选中的节点
  void _collectCheckedNodes(
    List<AppTreeNode<ExampleTreeData>> nodes,
    List<ExampleTreeData> checkedNodes,
  ) {
    for (final node in nodes) {
      if (node.isChecked) {
        checkedNodes.add(node.data);
      }
      _collectCheckedNodes(node.children, checkedNodes);
    }
  }

  /// 根据ID取消选中节点
  void _uncheckNodeById(String nodeId) {
    _uncheckNodeRecursive(_treeNodes, nodeId);
  }

  /// 递归查找并取消选中指定ID的节点
  void _uncheckNodeRecursive(List<AppTreeNode<ExampleTreeData>> nodes, String nodeId) {
    for (final node in nodes) {
      if (node.data.id == nodeId) {
        node.isChecked = false;
        node.isIndeterminate = false;
        node.clearCache();
        return;
      }
      _uncheckNodeRecursive(node.children, nodeId);
    }
  }

  /// 全选所有节点
  void _selectAllNodes() {
    setState(() {
      _setAllNodesChecked(_treeNodes, true);
      _selectedNodes.clear();
      _collectCheckedNodes(_treeNodes, _selectedNodes);
    });
  }

  /// 取消全选
  void _deselectAllNodes() {
    setState(() {
      _setAllNodesChecked(_treeNodes, false);
      _selectedNodes.clear();
    });
  }

  /// 递归设置所有节点的选中状态
  void _setAllNodesChecked(List<AppTreeNode<ExampleTreeData>> nodes, bool checked) {
    for (final node in nodes) {
      node.isChecked = checked;
      node.isIndeterminate = false;
      node.clearCache();
      _setAllNodesChecked(node.children, checked);
    }
  }

  /// 展开所有节点
  void _expandAllNodes() {
    setState(() {
      _setAllNodesExpanded(_treeNodes, true);
    });
  }

  /// 折叠所有节点
  void _collapseAllNodes() {
    setState(() {
      _setAllNodesExpanded(_treeNodes, false);
    });
  }

  /// 递归设置所有节点的展开状态
  void _setAllNodesExpanded(List<AppTreeNode<ExampleTreeData>> nodes, bool expanded) {
    for (final node in nodes) {
      if (node.children.isNotEmpty) {
        node.isExpanded = expanded;
      }
      _setAllNodesExpanded(node.children, expanded);
    }
  }

  /// 显示选中节点详情
  void _showSelectedNodesInfo() {
    if (_selectedNodes.isEmpty) return;

    final buffer = StringBuffer();
    buffer.writeln('选中的节点详情：\n');

    for (int i = 0; i < _selectedNodes.length; i++) {
      final node = _selectedNodes[i];
      buffer.writeln('${i + 1}. ${node.name} (ID: ${node.id})');
      if (node.parentId != null) {
        buffer.writeln('   父节点ID: ${node.parentId}');
      }
      if (node.parentIdList.isNotEmpty) {
        buffer.writeln('   层级路径: ${node.parentIdList.join(' > ')} > ${node.name}');
      }
      buffer.writeln();
    }

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('选中节点详情 (${_selectedNodes.length}个)'),
            content: SingleChildScrollView(child: Text(buffer.toString())),
            actions: [
              TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('关闭')),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _copySelectedNodesToClipboard();
                },
                child: const Text('复制到剪贴板'),
              ),
            ],
          ),
    );
  }

  /// 复制选中节点信息到剪贴板
  void _copySelectedNodesToClipboard() {
    final nodeNames = _selectedNodes.map((node) => node.name).join(', ');
    // 这里可以使用 Clipboard.setData 来复制到剪贴板
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('已复制 ${_selectedNodes.length} 个节点名称: $nodeNames')));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('树组件示例'), automaticallyImplyLeading: false),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildControlPanel(),
            const SizedBox(height: 16),
            _buildSelectedInfo(),
            const SizedBox(height: 16),
            Expanded(child: _buildTreeView()),
          ],
        ),
      ),
    );
  }

  /// 构建控制面板
  Widget _buildControlPanel() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('控制面板', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      labelText: '搜索节点',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Row(
                  children: [
                    Checkbox(
                      value: _showCheckbox,
                      onChanged: (value) {
                        setState(() {
                          _showCheckbox = value ?? false;
                        });
                      },
                    ),
                    const Text('显示复选框'),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            // 基础操作按钮
            Row(
              children: [
                ElevatedButton(onPressed: _loadSampleData, child: const Text('重新加载')),
                const SizedBox(width: 8),
                ElevatedButton(onPressed: _simulateError, child: const Text('模拟错误')),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _treeNodes = [];
                    });
                  },
                  child: const Text('清空数据'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // 树操作工具按钮
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _showCheckbox ? _selectAllNodes : null,
                  icon: const Icon(Icons.select_all, size: 16),
                  label: const Text('全选'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green.shade100,
                    foregroundColor: Colors.green.shade800,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _showCheckbox ? _deselectAllNodes : null,
                  icon: const Icon(Icons.deselect, size: 16),
                  label: const Text('取消全选'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange.shade100,
                    foregroundColor: Colors.orange.shade800,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _expandAllNodes,
                  icon: const Icon(Icons.unfold_more, size: 16),
                  label: const Text('展开所有'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade100,
                    foregroundColor: Colors.blue.shade800,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _collapseAllNodes,
                  icon: const Icon(Icons.unfold_less, size: 16),
                  label: const Text('折叠所有'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple.shade100,
                    foregroundColor: Colors.purple.shade800,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed:
                      _showCheckbox && _selectedNodes.isNotEmpty ? _showSelectedNodesInfo : null,
                  icon: const Icon(Icons.info_outline, size: 16),
                  label: const Text('选中详情'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal.shade100,
                    foregroundColor: Colors.teal.shade800,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建选中信息
  Widget _buildSelectedInfo() {
    if (!_showCheckbox || _selectedNodes.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('已选中节点 (${_selectedNodes.length})', style: Theme.of(context).textTheme.titleSmall),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children:
                  _selectedNodes
                      .map(
                        (node) => Chip(
                          label: Text(node.name),
                          onDeleted: () => _handleNodeSelected(node, false),
                        ),
                      )
                      .toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建树视图
  Widget _buildTreeView() {
    return Card(
      child: AppTree<ExampleTreeData>(
        nodes: _treeNodes,
        showCheckbox: _showCheckbox,
        searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
        onNodeTap: _handleNodeTap,
        onNodeSelected: _handleNodeSelected,
        isLoading: _isLoading,
        errorMessage: _errorMessage,
        onRetry: _retryLoad,
        style: AppTreeStyle.defaultStyle(),
      ),
    );
  }
}
